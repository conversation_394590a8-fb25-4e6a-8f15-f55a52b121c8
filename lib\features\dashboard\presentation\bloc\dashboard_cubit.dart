import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/error/failures.dart';
import '../../domain/usecases/load_dashboard_data_usecase.dart';
import 'dashboard_state.dart';

/// Dashboard Cubit for managing dashboard state
class DashboardCubit extends Cubit<DashboardState> {
  /// Use case for loading dashboard data.
  final LoadDashboardDataUseCase loadDashboardDataUseCase;

  /// Constructor for DashboardCubit.
  DashboardCubit(this.loadDashboardDataUseCase) : super(const DashboardInitial());

  /// Load dashboard data with parameters
  Future<void> loadDashboardData({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
  }) async {
    emit(const DashboardLoading()); // Indicate loading state

    final params = LoadDashboardDataParams(
      codeEtab: codeEtab,
      telephone: telephone,
      codeEtudiant: codeEtudiant,
    );

    final failureOrDashboardData = await loadDashboardDataUseCase(params);
    debugPrint('DASHBOARD CUBIT --> DashboardEntity: $failureOrDashboardData');

    failureOrDashboardData.fold(
      (failure) {
        // Handle failure and emit error state
        emit(DashboardError(_mapFailureToMessage(failure)));
      },
      (dashboardData) {
        // Emit success state with dashboard data
        emit(DashboardLoaded(dashboardData: dashboardData));
      },
    );
  }

  /// Refresh dashboard data
  Future<void> refresh({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
  }) async {
    await loadDashboardData(
      codeEtab: codeEtab,
      telephone: telephone,
      codeEtudiant: codeEtudiant,
    );
  }

  /// Map Failure to a user-friendly message
  String _mapFailureToMessage(Failure failure) {
    switch (failure.runtimeType) {
      case ServerFailure _:
        return (failure as ServerFailure).message;
      case AuthenticationFailure _:
        return (failure as AuthenticationFailure).message;
      case NetworkFailure _:
        return (failure as NetworkFailure).message;
      default:
        return 'Unexpected Error';
    }
  }
}
