import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:Kairos/features/schools/domain/entities/etablissement_utilisateur.dart';
import 'package:Kairos/features/schools/presentation/bloc/financial_status_cubit.dart';
import 'package:Kairos/features/schools/presentation/bloc/financial_status_state.dart';
import 'package:Kairos/features/authentication/data/datasources/auth_local_datasource.dart';
import 'package:Kairos/core/di/injection_container.dart';
import 'dart:convert'; // Import for base64 decoding
import 'alert.widget.dart';
import 'package:Kairos/features/profile/data/profile_type.enum.dart'; // Import the ProfileType enum


class ListeEtablissementUtilisateurWidget extends StatefulWidget {
  final List<EtablissementUtilisateur> userSchools; // Accept list of user schools

  const ListeEtablissementUtilisateurWidget({super.key, required this.userSchools});

  @override
  State<ListeEtablissementUtilisateurWidget> createState() => _ListeEtablissementUtilisateurWidgetState();
}

class _ListeEtablissementUtilisateurWidgetState extends State<ListeEtablissementUtilisateurWidget> {
  EtablissementUtilisateur? _currentSchool;

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      shrinkWrap: true,
      itemCount: widget.userSchools.length, // Use the provided list
      itemBuilder: (context, index) {
        final school = widget.userSchools[index];
        return BlocListener<FinancialStatusCubit, FinancialStatusState>(
          listener: (context, state) {
            if (state is FinancialStatusSuccess && _currentSchool?.codeEtab == school.codeEtab) {
              if(state.financialStatus.username != school.codeUtilisateur){
                return;
              }
              debugPrint('FinancialStatusSuccess --->: \\${state.financialStatus.enRegle}');
              if (!state.financialStatus.enRegle && state.financialStatus.username == school.codeUtilisateur) {
                showDialog(
                  context: context,
                  builder: (context) => AlertWidget(
                    message: "Vous avez des frais impayés. Veuillez régulariser votre situation afin d'accèder á votre espace",
                  ),
                );
              } else {
                if (_currentSchool?.profil == ProfileType.etudiant.code) {
                  _navigateToDashboard(context);
                } else if (_currentSchool?.profil == ProfileType.tuteur.code) {
                  _navigateToDossierSelection(context);
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text("Accès autorisé mais profil non reconnu: fontionnalité en cours de développement")),
                  );
                }
              }
            } else if (state is FinancialStatusError && _currentSchool?.codeEtab == school.codeEtab) {
              debugPrint('FinancialStatusError: \\${state.message}');
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text("Erreur: \\${state.message}")),
              );
            }
          },
          child: Card(
            color: Theme.of(context).scaffoldBackgroundColor,
            child: InkWell(
              // Prevent tap when financial status is loading
              onTap: () async {
                // Check if the cubit is currently loading
                if(school.profil == ProfileType.etudiant.code){

                final currentState = context.read<FinancialStatusCubit>().state;
                if (currentState is! FinancialStatusLoading) {
                  // Only handle selection if not loading
                  await _handleSchoolSelection(context, school);
                }
                } else {
                  setState(() {
                    _currentSchool = school;
                  _navigateToDossierSelection(context);
                  });
                }
              },
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 1.0),
                child: ListTile(
                  leading: school.logoEtablissement.isNotEmpty
                      ? Image.memory(
                          base64Decode(school.logoEtablissement),
                          width: 50, // Adjust size as needed
                          height: 50, // Adjust size as needed
                          fit: BoxFit.contain,
                        )
                      : Image.asset(
                          "assets/images/logo_iam.png",
                          width: 50, // Adjust size as needed
                          height: 50, // Adjust size as needed
                          fit: BoxFit.contain,
                        ),
                  title: Text(school.libelleEtab), // Display school name
               subtitle: Text.rich(
                 TextSpan(
                   children: [
                     const TextSpan(
                       text: "PROFILE: ",
                       style: TextStyle(fontWeight: FontWeight.bold),
                     ),
                     TextSpan(text: "${school.profil == 'ETU' ? 'ÉTUDIANT': 
                                       school.profil == 'PAR'? 'PARENT' : 
                                       school.profil == 'TUT' ? 'TUTEUR' : 'ÉTUDIANT'} "), // Display user profile
                     const TextSpan(
                       text: "ID: ",
                       style: TextStyle(fontWeight: FontWeight.bold),
                     ),
                     TextSpan(text: school.codeUtilisateur), // Display school ID
                   ],
                 ),
                ),
                // Conditionally show spinner or icon based on financial status loading state
                trailing: BlocBuilder<FinancialStatusCubit, FinancialStatusState>(
                  builder: (context, state) {
                    if (state is FinancialStatusLoading) {
                      // Show spinner when loading
                      return const SizedBox(
                        width: 16, // Slightly larger size for spinner
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      );
                    } else {
                      // Show arrow icon otherwise
                      return const Icon(Icons.arrow_forward_ios, size: 10);
                    }
                  },
                ),
              ),
            ),
          ),
        )
        );
      },
    );
  }

  /// Handle school selection and check financial status
  Future<void> _handleSchoolSelection(BuildContext context, EtablissementUtilisateur school) async {
    try {
      // Store the current school for later navigation
      setState(() {
        _currentSchool = school;
      });

      // Get phone number from SharedPreferences
      final authLocalDataSource = sl<AuthLocalDataSource>();
      final phoneNumber = await authLocalDataSource.getPhoneNumber();

      if (!context.mounted) return;

      if (phoneNumber != null) {
        // Trigger financial status check via BLoC
        context.read<FinancialStatusCubit>().checkFinancialStatus(
          codeEtab: school.codeEtab,
          telephone: phoneNumber,
          codeEtudiant: school.codeUtilisateur,
        );
      } else {
        // Handle case where phone number is not available
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("Erreur: Numéro de téléphone non disponible")),
        );
      }
    } catch (e) {
      // Handle any errors
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("Erreur: $e")),
        );
      }
    }
  }

  /// Navigate to dashboard with school data
  void _navigateToDashboard(BuildContext context) {
    if (_currentSchool != null) {
      Navigator.pushNamed(
        context,
        '/dashboard',
        arguments: _currentSchool,
      );
    }
  }

  /// Navigate to dossier selection page with school data
  void _navigateToDossierSelection(BuildContext context) {
    if (_currentSchool != null) {
      Navigator.pushNamed(
        context,
        '/dossier_selection',
        arguments: {
          'school': _currentSchool,
        },
      );
    }
  }
}